# -*- coding: utf-8 -*-
"""Klasifika<PERSON>_burung_mobilenetv2.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1yaLUA-nJ5hkhmaWEqq3rGfyK0RkwiofP
"""

import tensorflow as tf
import zipfile
import os
from google.colab import drive
from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.models import Model
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.utils.class_weight import compute_class_weight
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import accuracy_score, classification_report
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix
import glob
from PIL import Image

"""# Define the input shape and number of classes"""

input_shape = (224, 224, 3)
num_classes = 5

"""# Define data generators with additional data augmentation"""

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=20,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    vertical_flip=True,
    fill_mode='nearest',
)

valid_datagen = ImageDataGenerator(
    rescale=1./255
)

# Function to load and prepare data for cross validation
def load_data_for_cv():
    """Load all data and prepare for cross validation"""
    #input data
    drive.mount('/content/drive')
    # Salin file zip dari Drive ke penyimpanan lokal Colab (jauh lebih cepat)
    !cp "/content/drive/MyDrive/images.zip" "/content/"

    # Ekstrak file zip di penyimpanan lokal
    import zipfile
    with zipfile.ZipFile("/content/images.zip", 'r') as zip_ref:
        zip_ref.extractall("/content/dataset") # Ekstrak ke folder /content/dataset

    # Combine train and validation data for cross validation
    train_data_dir = '/content/dataset/images/train'
    valid_data_dir = '/content/dataset/images/validation'

    # Get all image paths and labels
    image_paths = []
    labels = []

    # Load from train directory
    for class_name in os.listdir(train_data_dir):
        class_path = os.path.join(train_data_dir, class_name)
        if os.path.isdir(class_path):
            for img_file in os.listdir(class_path):
                if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    image_paths.append(os.path.join(class_path, img_file))
                    labels.append(class_name)

    # Load from validation directory
    for class_name in os.listdir(valid_data_dir):
        class_path = os.path.join(valid_data_dir, class_name)
        if os.path.isdir(class_path):
            for img_file in os.listdir(class_path):
                if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    image_paths.append(os.path.join(class_path, img_file))
                    labels.append(class_name)

    # Create label mapping
    unique_labels = sorted(list(set(labels)))
    label_to_idx = {label: idx for idx, label in enumerate(unique_labels)}
    idx_to_label = {idx: label for label, idx in label_to_idx.items()}

    # Convert labels to indices
    label_indices = [label_to_idx[label] for label in labels]

    return np.array(image_paths), np.array(label_indices), label_to_idx, idx_to_label

# Set batch size
batch_size = 128

# Function to create MobileNetV2 model with transfer learning
def create_mobilenetv2_model():
    """Create MobileNetV2 model with transfer learning"""
    # Load MobileNetV2 as base model
    base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=input_shape)

    # Freeze layers in base model
    for layer in base_model.layers:
        layer.trainable = False

    # Add custom head
    x = base_model.output
    x = GlobalAveragePooling2D()(x)
    x = Dropout(0.5)(x)
    x = Dense(1024, activation='relu')(x)
    x = Dropout(0.5)(x)
    predictions = Dense(num_classes, activation='softmax')(x)

    # Combine base model and custom head
    model = Model(inputs=base_model.input, outputs=predictions)

    # Fine-tune the model by unfreezing some layers
    for layer in base_model.layers[:-10]:
        layer.trainable = False

    # Implement learning rate scheduling
    lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
        initial_learning_rate=1e-4,
        decay_steps=1000,
        decay_rate=0.9
    )
    optimizer = Adam(learning_rate=lr_schedule)

    # Compile the model
    model.compile(optimizer=optimizer, loss='categorical_crossentropy', metrics=['accuracy'])

    return model

# Function to load and preprocess images
def load_and_preprocess_image(image_path, target_size=(224, 224)):
    """Load and preprocess a single image"""
    try:
        image = Image.open(image_path)
        image = image.convert('RGB')
        image = image.resize(target_size)
        image_array = np.array(image) / 255.0
        return image_array
    except Exception as e:
        print(f"Error loading image {image_path}: {e}")
        return None

# Cross Validation Implementation
def perform_cross_validation(image_paths, labels, n_splits=5):
    """Perform cross validation with transfer learning"""

    # Initialize StratifiedKFold
    skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)

    # Store results
    cv_scores = []
    fold_histories = []
    all_predictions = []
    all_true_labels = []

    print(f"Starting {n_splits}-fold cross validation...")

    for fold, (train_idx, val_idx) in enumerate(skf.split(image_paths, labels)):
        print(f"\n=== Fold {fold + 1}/{n_splits} ===")

        # Split data
        train_paths = image_paths[train_idx]
        val_paths = image_paths[val_idx]
        train_labels = labels[train_idx]
        val_labels = labels[val_idx]

        # Load and preprocess images
        print("Loading training images...")
        X_train = []
        y_train = []
        for i, path in enumerate(train_paths):
            img = load_and_preprocess_image(path)
            if img is not None:
                X_train.append(img)
                y_train.append(train_labels[i])

        print("Loading validation images...")
        X_val = []
        y_val = []
        for i, path in enumerate(val_paths):
            img = load_and_preprocess_image(path)
            if img is not None:
                X_val.append(img)
                y_val.append(val_labels[i])

        # Convert to numpy arrays
        X_train = np.array(X_train)
        X_val = np.array(X_val)
        y_train = tf.keras.utils.to_categorical(y_train, num_classes)
        y_val = tf.keras.utils.to_categorical(y_val, num_classes)

        print(f"Training set size: {len(X_train)}")
        print(f"Validation set size: {len(X_val)}")

        # Create model for this fold
        model = create_mobilenetv2_model()

        # Implement early stopping callback
        early_stopping = tf.keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=3,
            restore_best_weights=True
        )

        # Data augmentation for training
        train_datagen_cv = ImageDataGenerator(
            rotation_range=20,
            width_shift_range=0.2,
            height_shift_range=0.2,
            shear_range=0.2,
            zoom_range=0.2,
            horizontal_flip=True,
            vertical_flip=True,
            fill_mode='nearest'
        )

        # Train the model
        history = model.fit(
            train_datagen_cv.flow(X_train, y_train, batch_size=batch_size),
            epochs=30,
            validation_data=(X_val, y_val),
            callbacks=[early_stopping],
            verbose=1,
            steps_per_epoch=len(X_train) // batch_size
        )

        # Evaluate on validation set
        val_loss, val_accuracy = model.evaluate(X_val, y_val, verbose=0)
        cv_scores.append(val_accuracy)
        fold_histories.append(history)

        # Make predictions for this fold
        val_predictions = model.predict(X_val)
        val_pred_labels = np.argmax(val_predictions, axis=1)
        val_true_labels = np.argmax(y_val, axis=1)

        all_predictions.extend(val_pred_labels)
        all_true_labels.extend(val_true_labels)

        print(f"Fold {fold + 1} Validation Accuracy: {val_accuracy:.4f}")

        # Clear memory
        del model, X_train, X_val, y_train, y_val
        tf.keras.backend.clear_session()

    return cv_scores, fold_histories, all_predictions, all_true_labels

# Main execution
if __name__ == "__main__":
    # Load data for cross validation
    print("Loading data for cross validation...")
    image_paths, labels, label_to_idx, idx_to_label = load_data_for_cv()

    print(f"Total images: {len(image_paths)}")
    print(f"Number of classes: {len(label_to_idx)}")
    print(f"Classes: {list(label_to_idx.keys())}")

    # Perform cross validation
    cv_scores, fold_histories, all_predictions, all_true_labels = perform_cross_validation(
        image_paths, labels, n_splits=5
    )

    # Print cross validation results
    print("\n=== Cross Validation Results ===")
    print(f"Individual fold accuracies: {[f'{score:.4f}' for score in cv_scores]}")
    print(f"Mean CV Accuracy: {np.mean(cv_scores):.4f} (+/- {np.std(cv_scores) * 2:.4f})")
    print(f"Standard Deviation: {np.std(cv_scores):.4f}")

    # Create confusion matrix from all predictions
    class_labels = [idx_to_label[i] for i in range(len(idx_to_label))]
    conf_matrix = confusion_matrix(all_true_labels, all_predictions)

    # Plot the confusion matrix
    plt.figure(figsize=(12, 8))
    sns.heatmap(conf_matrix, annot=True, fmt='d',
                xticklabels=class_labels, yticklabels=class_labels,
                cmap='Blues')
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    plt.title('Confusion Matrix - Cross Validation Results')
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.show()

    # Print classification report
    print("\n=== Classification Report ===")
    print(classification_report(all_true_labels, all_predictions,
                              target_names=class_labels))

    # Plot training history for each fold
    plt.figure(figsize=(15, 10))

    # Plot accuracy
    plt.subplot(2, 2, 1)
    for i, history in enumerate(fold_histories):
        plt.plot(history.history['accuracy'], label=f'Fold {i+1} Train')
        plt.plot(history.history['val_accuracy'], label=f'Fold {i+1} Val', linestyle='--')
    plt.title('Model Accuracy Across Folds')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()
    plt.grid(True)

    # Plot loss
    plt.subplot(2, 2, 2)
    for i, history in enumerate(fold_histories):
        plt.plot(history.history['loss'], label=f'Fold {i+1} Train')
        plt.plot(history.history['val_loss'], label=f'Fold {i+1} Val', linestyle='--')
    plt.title('Model Loss Across Folds')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    # Plot CV scores
    plt.subplot(2, 2, 3)
    plt.bar(range(1, len(cv_scores) + 1), cv_scores)
    plt.axhline(y=np.mean(cv_scores), color='r', linestyle='--',
                label=f'Mean: {np.mean(cv_scores):.4f}')
    plt.title('Cross Validation Scores')
    plt.xlabel('Fold')
    plt.ylabel('Accuracy')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.show()

    # Train final model on all data for deployment
    print("\n=== Training Final Model on All Data ===")

    # Load all images
    print("Loading all images for final model...")
    X_all = []
    y_all = []
    for i, path in enumerate(image_paths):
        img = load_and_preprocess_image(path)
        if img is not None:
            X_all.append(img)
            y_all.append(labels[i])

    X_all = np.array(X_all)
    y_all = tf.keras.utils.to_categorical(y_all, num_classes)

    # Create and train final model
    final_model = create_mobilenetv2_model()

    # Data augmentation for final training
    final_datagen = ImageDataGenerator(
        rotation_range=20,
        width_shift_range=0.2,
        height_shift_range=0.2,
        shear_range=0.2,
        zoom_range=0.2,
        horizontal_flip=True,
        vertical_flip=True,
        fill_mode='nearest'
    )

    # Train final model
    final_history = final_model.fit(
        final_datagen.flow(X_all, y_all, batch_size=batch_size),
        epochs=20,  # Fewer epochs since we know the optimal range from CV
        verbose=1,
        steps_per_epoch=len(X_all) // batch_size
    )

    # Save the final model
    final_model.save('birds_classification_cv.h5')
    print("Final model saved as 'birds_classification_cv.h5'")

    # Display download link (for Colab)
    try:
        from IPython.display import FileLink
        FileLink(r'birds_classification_cv.h5')
    except ImportError:
        print("Model saved successfully!")